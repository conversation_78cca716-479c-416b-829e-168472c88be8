<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "mybatis-3-mapper.dtd">
<mapper namespace="com.xq.tmall.dao.AdminMapper">
    <insert id="insertOne" parameterType="com.xq.tmall.entity.Admin">
        INSERT admin(admin_name,admin_nickname,admin_password,admin_profile_picture_src)
            VALUES (#{admin_name},
            #{admin_nickname},
            #{admin_password},
            #{admin_profile_picture_src})
    </insert>
    <update id="updateOne" parameterType="com.xq.tmall.entity.Admin">
        UPDATE admin
        <set>
            <if test="admin_profile_picture_src != null">admin_profile_picture_src = #{admin_profile_picture_src},</if>
            <if test="admin_nickname != null">admin_nickname = #{admin_nickname},</if>
            <if test="admin_password != null">admin_password = #{admin_password},</if>
        </set>
        <where>
            admin_id = #{admin_id}
        </where>
    </update>
    <select id="selectAdminList" resultType="com.xq.tmall.entity.Admin">
        SELECT admin_id,admin_name,admin_nickname,admin_profile_picture_src FROM admin
        <if test="pageUtil != null">
            LIMIT #{pageUtil.index},#{pageUtil.count}
        </if>
        <where>
            <if test="admin_name != null">
                admin_name LIKE concat('%',#{admin_name},'%')
            </if>
        </where>
    </select>
    <select id="selectOne" resultType="com.xq.tmall.entity.Admin">
        SELECT admin_id,admin_name,admin_nickname,admin_profile_picture_src FROM admin
        <where>
            <if test="admin_id != null">
                and admin_id = #{admin_id}
            </if>
            <if test="admin_name != null">
                and admin_name = #{admin_name}
            </if>
        </where>
    </select>
    <select id="selectByLogin" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM admin
        <where>
            <if test="admin_name != null">
            and admin_name = #{admin_name}
            </if>
            <if test="admin_password != null">
            and admin_password = #{admin_password}
            </if>
        </where>
    </select>
    <select id="selectTotal" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM admin
        <where>
            <if test="admin_name != null">admin_name LIKE concat('%',#{admin_name},'%')</if>
        </where>
    </select>
    <select id="getAdmin" resultType="com.xq.tmall.entity.Admin">
        SELECT admin_id,admin_name,admin_nickname,admin_profile_picture_src FROM admin
        where admin_name = #{userName} and admin_password = #{password}
    </select>
</mapper>
---
title: shop
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# shop

Base URLs:

* <a href="http://dev-cn.your-api-server.com">开发环境: http://dev-cn.your-api-server.com</a>

# Authentication

# 前台天猫-主页

<a id="opIdgoToPageUsingGET_4"></a>

## GET 转到前台天猫-主页

GET /

转到前台天猫-主页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToErrorPageUsingGET"></a>

## GET 转到前台天猫-错误页

GET /error

转到前台天猫-错误页

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgetProductByNavUsingGET"></a>

## GET 获取主页分类下产品信息

GET /product/nav/{category_id}

获取主页分类下产品信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|category_id|path|integer(int32)| 是 |category_id|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

# 前台天猫-地址

<a id="opIdgetAddressByAreaIdUsingGET"></a>

## GET 根据address_areaId获取地址信息

GET /address/{areaId}

根据address_areaId获取地址信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|areaId|path|string| 是 |areaId|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

# 后台管理-主页

<a id="opIdgoToPageUsingGET_1"></a>

## GET 转到后台管理-主页

GET /admin

转到后台管理-主页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToPageByAjaxUsingGET"></a>

## GET 转到后台管理-home主页

GET /admin/home

转到后台管理-home主页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgetChartDataByDateUsingGET"></a>

## GET 按日期查询图表数据

GET /admin/home/<USER>

按日期查询图表数据

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|beginDate|query|string| 否 |beginDate|
|endDate|query|string| 否 |endDate|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

# 后台管理-账户页

<a id="opIdgoToPageUsingGET"></a>

## GET 转到后台管理-账户页

GET /admin/account

转到后台管理-账户页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdlogoutUsingGET"></a>

## GET 退出当前账号

GET /admin/account/logout

退出当前账号

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdupdateAdminUsingPUT"></a>

## PUT 更新管理员信息

PUT /admin/account/{admin_id}

更新管理员信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|admin_id|path|string| 是 |admin_id|
|admin_newPassword|query|string| 否 |admin_newPassword|
|admin_nickname|query|string| 是 |admin_nickname|
|admin_password|query|string| 否 |admin_password|
|admin_profile_picture_src|query|string| 否 |admin_profile_picture_src|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIduploadAdminHeadImageUsingPOST"></a>

## POST 管理员头像上传

POST /admin/uploadAdminHeadImage

管理员头像上传

> Body 请求参数

```yaml
file: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |file|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

# 后台管理-分类页

<a id="opIdgoToPageUsingGET_3"></a>

## GET 转到后台管理-分类页

GET /admin/category

转到后台管理-分类页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdaddCategoryUsingPOST"></a>

## POST 添加分类信息

POST /admin/category

添加分类信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|category_image_src|query|string| 是 |category_image_src|
|category_name|query|string| 是 |category_name|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIddeleteProductByIdUsingGET"></a>

## GET 按ID删除分类并返回最新结果

GET /admin/category/del/{id}

按ID删除分类并返回最新结果

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer(int32)| 是 |id|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToAddPageUsingGET"></a>

## GET 转到后台管理-分类添加页

GET /admin/category/new

转到后台管理-分类添加页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdupdateCategoryUsingPUT"></a>

## PUT 更新分类信息

PUT /admin/category/{category_id}

更新分类信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|category_id|path|integer(int32)| 是 |category_id|
|category_image_src|query|string| 是 |category_image_src|
|category_name|query|string| 是 |category_name|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToDetailsPageUsingGET"></a>

## GET 转到后台管理-分类详情页

GET /admin/category/{cid}

转到后台管理-分类详情页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|cid|path|integer(int32)| 是 |cid|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgetCategoryBySearchUsingGET"></a>

## GET 按条件查询分类

GET /admin/category/{index}/{count}

按条件查询分类

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|count|path|integer(int32)| 是 |count|
|index|path|integer(int32)| 是 |index|
|category_name|query|string| 否 |category_name|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIduploadCategoryImageUsingPOST"></a>

## POST 上传分类图片

POST /admin/uploadCategoryImage

上传分类图片

> Body 请求参数

```yaml
file: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |file|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

# 后台管理-登录页

<a id="opIdgoToPageUsingGET_2"></a>

## GET 转到后台管理-登录页

GET /admin/login

转到后台管理-登录页

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgetVerCodeUsingGET"></a>

## GET 获取验证码

GET /admin/login/code

获取图片验证码

> 返回示例

> 200 Response

```
{"code":"string","img":"string","verToken":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[ApiVerCodeResp](#schemaapivercoderesp)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

状态码 **200**

*ApiVerCodeResp*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|string|false|none||验证码|
|» img|string|false|none||验证码图片|
|» verToken|string|false|none||verToken|

<a id="opIdcheckLoginUsingPOST"></a>

## POST 登陆验证

POST /admin/login/doLogin

登陆验证

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|password|query|string| 是 |password|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|username|query|string| 是 |username|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgetAdminProfilePictureUsingGET"></a>

## GET 获取管理员头像路径

GET /admin/login/profile_picture

获取管理员头像路径

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|username|query|string| 是 |username|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

# 后台管理-订单页

<a id="opIdgoToPageUsingGET_11"></a>

## GET 转到后台管理-订单页

GET /admin/order

转到后台管理-订单页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgetOrderBySearchUsingGET"></a>

## GET 更新订单信息

GET /admin/order/{index}/{count}

更新订单信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|count|path|integer(int32)| 是 |count|
|index|path|integer(int32)| 是 |index|
|isDesc|query|boolean| 否 |isDesc|
|orderBy|query|string| 否 |orderBy|
|productOrder_code|query|string| 否 |productOrder_code|
|productOrder_mobile|query|string| 否 |productOrder_mobile|
|productOrder_post|query|string| 否 |productOrder_post|
|productOrder_receiver|query|string| 否 |productOrder_receiver|
|productOrder_status_array|query|string(byte)| 否 |productOrder_status_array|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToDetailsPageUsingGET_1"></a>

## GET 转到后台管理-订单详情页

GET /admin/order/{oid}

转到后台管理-订单详情页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|oid|path|integer(int32)| 是 |oid|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdupdateOrderUsingPUT"></a>

## PUT 更新订单信息

PUT /admin/order/{order_id}

更新订单信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|order_id|path|string| 是 |order_id|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

# 后台管理-产品页

<a id="opIdgoToPageUsingGET_12"></a>

## GET 转到后台管理-产品页

GET /admin/product

转到后台管理-产品页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdaddProductUsingPOST"></a>

## POST 添加产品信息

POST /admin/product

添加产品信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|productDetailsImageList|query|array[string]| 否 |productDetailsImageList|
|productSingleImageList|query|array[string]| 否 |productSingleImageList|
|product_category_id|query|integer(int32)| 是 |product_category_id|
|product_isEnabled|query|integer(int32)| 是 |product_isEnabled|
|product_name|query|string| 是 |product_name|
|product_price|query|number(double)| 是 |product_price|
|product_sale_price|query|number(double)| 是 |product_sale_price|
|product_title|query|string| 是 |product_title|
|propertyJson|query|string| 是 |propertyJson|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdimportFileUsingPOST"></a>

## POST 导入产品excel文件

POST /admin/product/importFile

导入产品excel文件

> Body 请求参数

```yaml
file: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |file|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToAddPageUsingGET_1"></a>

## GET 转到后台管理-产品添加页

GET /admin/product/new

转到后台管理-产品添加页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdimportFileTemplateUsingGET"></a>

## GET 下载产品导出数据模板

GET /admin/product/template

下载产品导出数据模板

> 返回示例

> 200 Response

```
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgetProductBySearchUsingGET"></a>

## GET 按条件查询产品

GET /admin/product/{index}/{count}

按条件查询产品

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|count|path|integer(int32)| 是 |count|
|index|path|integer(int32)| 是 |index|
|category_id|query|integer(int32)| 否 |category_id|
|isDesc|query|boolean| 否 |isDesc|
|orderBy|query|string| 否 |orderBy|
|product_isEnabled_array|query|string(byte)| 否 |product_isEnabled_array|
|product_name|query|string| 否 |product_name|
|product_price|query|number(double)| 否 |product_price|
|product_sale_price|query|number(double)| 否 |product_sale_price|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToDetailsPageUsingGET_2"></a>

## GET 转到后台管理-产品详情页

GET /admin/product/{pid}

转到后台管理-产品详情页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pid|path|integer(int32)| 是 |pid|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdupdateProductUsingPUT"></a>

## PUT 更新产品信息

PUT /admin/product/{product_id}

更新产品信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|product_id|path|integer(int32)| 是 |product_id|
|productDetailsImageList|query|array[string]| 否 |productDetailsImageList|
|productSingleImageList|query|array[string]| 否 |productSingleImageList|
|product_category_id|query|integer(int32)| 是 |product_category_id|
|product_isEnabled|query|integer(int32)| 是 |product_isEnabled|
|product_name|query|string| 是 |product_name|
|product_price|query|number(double)| 是 |product_price|
|product_sale_price|query|number(double)| 是 |product_sale_price|
|product_title|query|string| 是 |product_title|
|propertyAddJson|query|string| 是 |propertyAddJson|
|propertyDeleteList|query|array[integer]| 否 |propertyDeleteList|
|propertyUpdateJson|query|string| 是 |propertyUpdateJson|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIddeleteProductImageByIdUsingDELETE"></a>

## DELETE 按ID删除产品图片并返回最新结果

DELETE /admin/productImage/{productImage_id}

按ID删除产品图片并返回最新结果

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|productImage_id|path|integer(int32)| 是 |productImage_id|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|204|[No Content](https://tools.ietf.org/html/rfc7231#section-6.3.5)|No Content|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|

### 返回数据结构

<a id="opIdgetPropertyByCategoryIdUsingGET"></a>

## GET 按类型ID查询属性

GET /admin/property/type/{property_category_id}

按类型ID查询属性

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|property_category_id|path|integer(int32)| 是 |property_category_id|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIduploadProductImageUsingPOST"></a>

## POST 上传产品图片

POST /admin/uploadProductImage

上传产品图片

> Body 请求参数

```yaml
file: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|imageType|query|string| 是 |imageType|
|lastAccessedTime|query|integer(int64)| 否 |none|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |file|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIddeleteProductByIdUsingGET_1"></a>

## GET 按ID删除产品并返回最新结果

GET /product/del/{id}

按ID删除产品并返回最新结果

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer(int32)| 是 |id|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

# 后台管理-属性页

<a id="opIdgoToPageUsingGET_13"></a>

## GET 转到后台管理-属性页

GET /admin/property

转到后台管理-属性页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIddeleteProductByIdUsingGET_2"></a>

## GET 按ID删除属性并返回最新结果

GET /admin/property/del/{id}

按ID删除属性并返回最新结果

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer(int32)| 是 |id|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToAddPageUsingGET_2"></a>

## GET 转到后台管理-属性添加页

GET /admin/property/new

转到后台管理-属性添加页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdaddCategoryUsingPOST_1"></a>

## POST 添加属性信息

POST /admin/property/{category_id}

添加属性信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|category_id|path|integer(int32)| 是 |category_id|
|property_name|query|string| 是 |property_name|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToDetailsPageUsingGET_3"></a>

## GET 转到后台管理-属性详情页

GET /admin/property/{cid}

转到后台管理-属性详情页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|cid|path|integer(int32)| 是 |cid|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgetCategoryBySearchUsingGET_1"></a>

## GET 按条件查询属性

GET /admin/property/{index}/{count}

按条件查询属性

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|count|path|integer(int32)| 是 |count|
|index|path|integer(int32)| 是 |index|
|category_id|query|integer(int32)| 否 |category_id|
|property_name|query|string| 否 |property_name|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdupdateCategoryUsingPUT_1"></a>

## PUT 更新属性信息

PUT /admin/property/{property_id}/{category_id}

更新属性信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|category_id|path|integer(int32)| 是 |category_id|
|property_id|path|integer(int32)| 是 |property_id|
|property_name|query|string| 是 |property_name|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

# 后台管理-评论页

<a id="opIdgoToPageUsingGET_14"></a>

## GET 转到后台管理-评论页

GET /admin/review

转到后台管理-评论页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIddeleteProductByIdUsingGET_3"></a>

## GET 按ID删除评论并返回最新结果

GET /admin/review/del/{id}

按ID删除评论并返回最新结果

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer(int32)| 是 |id|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToDetailsPageUsingGET_4"></a>

## GET 转到后台管理-评论详情页

GET /admin/review/{cid}

转到后台管理-评论详情页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|cid|path|integer(int32)| 是 |cid|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgetReviewBySearchUsingGET"></a>

## GET 按条件查询评论

GET /admin/review/{index}/{count}

按条件查询评论

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|count|path|integer(int32)| 是 |count|
|index|path|integer(int32)| 是 |index|
|isDesc|query|boolean| 否 |isDesc|
|orderBy|query|string| 否 |orderBy|
|review_content|query|string| 否 |review_content|
|review_createDate|query|string| 否 |review_createDate|
|review_name|query|string| 否 |review_name|
|review_userName|query|string| 否 |review_userName|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

# 后台管理-用户页

<a id="opIdgoUserManagePageUsingGET"></a>

## GET 转到后台管理-用户页

GET /admin/user

转到后台管理-用户页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIddeleteProductByIdUsingGET_4"></a>

## GET 按ID删除用户并返回最新结果

GET /admin/user/del/{id}

按ID删除用户并返回最新结果

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer(int32)| 是 |id|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgetUserBySearchUsingGET"></a>

## GET 按条件查询用户

GET /admin/user/{index}/{count}

按条件查询用户

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|count|path|integer(int32)| 是 |count|
|index|path|integer(int32)| 是 |index|
|isDesc|query|boolean| 否 |isDesc|
|orderBy|query|string| 否 |orderBy|
|user_gender_array|query|string(byte)| 否 |user_gender_array|
|user_name|query|string| 否 |user_name|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgetUserByIdUsingGET"></a>

## GET 转到后台管理-用户详情页

GET /admin/user/{uid}

转到后台管理-用户详情页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|uid|path|integer(int32)| 是 |uid|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

# 前台天猫-订单

<a id="opIdgoToCartPageUsingGET"></a>

## GET 转到前台天猫-购物车页

GET /cart

转到前台天猫-购物车页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToPageSimpleUsingGET"></a>

## GET 转到前台天猫-订单列表页

GET /order

转到前台天猫-订单列表页

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdcreateOrderByOneUsingPOST"></a>

## POST 创建新订单-单订单项

POST /order

创建新订单-单订单项

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|addressId|query|string| 是 |addressId|
|cityAddressId|query|string| 是 |cityAddressId|
|creationTime|query|integer(int64)| 否 |none|
|districtAddressId|query|string| 是 |districtAddressId|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|orderItem_number|query|integer(int32)| 是 |orderItem_number|
|orderItem_product_id|query|integer(int32)| 是 |orderItem_product_id|
|productOrder_detail_address|query|string| 是 |productOrder_detail_address|
|productOrder_mobile|query|string| 是 |productOrder_mobile|
|productOrder_post|query|string| 是 |productOrder_post|
|productOrder_receiver|query|string| 是 |productOrder_receiver|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|userMessage|query|string| 是 |userMessage|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdorderCloseUsingPUT"></a>

## PUT 更新订单信息为交易关闭

PUT /order/close/{order_code}

更新订单信息为交易关闭

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|order_code|path|string| 是 |order_code|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToOrderConfirmPageUsingGET"></a>

## GET 转到前台天猫-订单确认页

GET /order/confirm/{order_code}

转到前台天猫-订单确认页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|order_code|path|string| 是 |order_code|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToOrderConfirmPageByCartUsingGET"></a>

## GET 转到前台天猫-购物车订单建立页

GET /order/create/byCart

转到前台天猫-购物车订单建立页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|order_item_list|query|array[integer]| 否 |order_item_list|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToOrderConfirmPageUsingGET_1"></a>

## GET 转到前台天猫-订单建立页

GET /order/create/{product_id}

转到前台天猫-订单建立页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|product_id|path|integer(int32)| 是 |product_id|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|product_number|query|integer(int32)| 否 |product_number|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdorderDeliveryUsingGET"></a>

## GET 更新订单信息为已发货，待确认

GET /order/delivery/{order_code}

更新订单信息为已发货，待确认

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|order_code|path|string| 是 |order_code|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdcreateOrderByListUsingPOST"></a>

## POST 创建新订单-多订单项

POST /order/list

创建新订单-多订单项

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|addressId|query|string| 是 |addressId|
|cityAddressId|query|string| 是 |cityAddressId|
|creationTime|query|integer(int64)| 否 |none|
|districtAddressId|query|string| 是 |districtAddressId|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|orderItemJSON|query|string| 是 |orderItemJSON|
|productOrder_detail_address|query|string| 是 |productOrder_detail_address|
|productOrder_mobile|query|string| 是 |productOrder_mobile|
|productOrder_post|query|string| 是 |productOrder_post|
|productOrder_receiver|query|string| 是 |productOrder_receiver|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToOrderPaySuccessPageUsingGET"></a>

## GET 转到前台天猫-订单支付成功页

GET /order/pay/success/{order_code}

转到前台天猫-订单支付成功页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|order_code|path|string| 是 |order_code|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToOrderPayPageUsingGET"></a>

## GET 转到前台天猫-订单支付页

GET /order/pay/{order_code}

转到前台天猫-订单支付页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|order_code|path|string| 是 |order_code|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdorderPayUsingPUT"></a>

## PUT 更新订单信息为已支付，待发货

PUT /order/pay/{order_code}

更新订单信息为已支付，待发货

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|order_code|path|string| 是 |order_code|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToOrderSuccessPageUsingGET"></a>

## GET 转到前台天猫-订单完成页

GET /order/success/{order_code}

转到前台天猫-订单完成页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|order_code|path|string| 是 |order_code|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdorderSuccessUsingPUT"></a>

## PUT 更新订单信息为交易成功

PUT /order/success/{order_code}

更新订单信息为交易成功

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|order_code|path|string| 是 |order_code|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToPageUsingGET_6"></a>

## GET 退出当前账号

GET /order/{index}/{count}

退出当前账号

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|count|path|integer(int32)| 是 |count|
|index|path|integer(int32)| 是 |index|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|status|query|integer(int32)| 否 |status|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdupdateOrderItemUsingPUT"></a>

## PUT 更新购物车订单项数量

PUT /orderItem

更新购物车订单项数量

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|orderItemMap|query|string| 是 |orderItemMap|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdcreateOrderItemUsingPOST"></a>

## POST 创建订单项-购物车

POST /orderItem/create/{product_id}

创建订单项-购物车

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|product_id|path|integer(int32)| 是 |product_id|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|product_number|query|integer(int32)| 否 |product_number|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIddeleteOrderItemUsingDELETE"></a>

## DELETE 删除订单项-购物车

DELETE /orderItem/{orderItem_id}

删除订单项-购物车

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|orderItem_id|path|integer(int32)| 是 |orderItem_id|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|204|[No Content](https://tools.ietf.org/html/rfc7231#section-6.3.5)|No Content|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|

### 返回数据结构

# 前台天猫-产品详情页

<a id="opIdguessYouLikeUsingGET"></a>

## GET 加载猜你喜欢列表

GET /guess/{cid}

加载猜你喜欢列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|cid|path|integer(int32)| 是 |cid|
|guessNumber|query|integer(int32)| 是 |guessNumber|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToPageUsingGET_7"></a>

## GET 转到前台天猫-产品详情页

GET /product/{pid}

转到前台天猫-产品详情页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pid|path|string| 是 |pid|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdloadProductPropertyListUsingGET"></a>

## GET 按产品ID加载产品属性列表

GET /property/{pid}

按产品ID加载产品属性列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pid|path|string| 是 |pid|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdloadProductReviewListUsingGET"></a>

## GET 按产品ID加载产品评论列表

GET /review/{pid}

按产品ID加载产品评论列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pid|path|string| 是 |pid|
|count|query|integer(int32)| 是 |count|
|index|query|integer(int32)| 是 |index|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

# 前台天猫-登陆页

<a id="opIdgoToPageUsingGET_5"></a>

## GET 转到前台天猫-登录页

GET /login

转到前台天猫-登录页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgetVerCodeUsingGET_1"></a>

## GET 登录验证码

GET /login/code

登录验证码

> 返回示例

> 200 Response

```
{"code":"string","img":"string","verToken":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[ApiVerCodeResp](#schemaapivercoderesp)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

状态码 **200**

*ApiVerCodeResp*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|string|false|none||验证码|
|» img|string|false|none||验证码图片|
|» verToken|string|false|none||verToken|

<a id="opIdcheckLoginUsingPOST_1"></a>

## POST 登陆验证

POST /login/doLogin

登陆验证

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|password|query|string| 是 |password|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|username|query|string| 是 |username|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdlogoutUsingGET_1"></a>

## GET 退出当前账号

GET /login/logout

退出当前账号

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

# 前台天猫-产品搜索列表

<a id="opIdgoToPageUsingGET_8"></a>

## GET 转到前台天猫-产品搜索列表页

GET /product

转到前台天猫-产品搜索列表页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|category_id|query|integer(int32)| 否 |category_id|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|product_name|query|string| 否 |product_name|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdsearchProductUsingGET"></a>

## GET 产品高级查询

GET /product/{index}/{count}

产品高级查询

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|count|path|integer(int32)| 是 |count|
|index|path|integer(int32)| 是 |index|
|category_id|query|integer(int32)| 否 |category_id|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|isDesc|query|boolean| 否 |isDesc|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|orderBy|query|string| 否 |orderBy|
|product_name|query|string| 否 |product_name|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

# 前台天猫-用户注册

<a id="opIdgoToPageUsingGET_9"></a>

## GET 转到前台天猫-用户注册页

GET /register

转到前台天猫-用户注册页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|map|query|string| 否 |map|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdregisterUsingPOST"></a>

## POST 天猫前台-用户注册

POST /register/doRegister

天猫前台-用户注册

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|user_address|query|string| 是 |user_address|
|user_birthday|query|string| 是 |user_birthday|
|user_gender|query|string| 是 |user_gender|
|user_name|query|string| 是 |user_name|
|user_nickname|query|string| 是 |user_nickname|
|user_password|query|string| 是 |user_password|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

# 前台天猫-评论添加

<a id="opIdgetReviewInfoUsingGET"></a>

## GET 获取产品评论信息

GET /review

获取产品评论信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|count|query|integer(int32)| 是 |count|
|index|query|integer(int32)| 是 |index|
|product_id|query|integer(int32)| 是 |product_id|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdaddReviewUsingPOST"></a>

## POST 添加一条评论

POST /review

添加一条评论

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|orderItem_id|query|integer(int32)| 是 |orderItem_id|
|review_content|query|string| 是 |review_content|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToPageUsingGET_10"></a>

## GET 转到前台天猫-评论添加页

GET /review/{orderItem_id}

转到前台天猫-评论添加页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|orderItem_id|path|integer(int32)| 是 |orderItem_id|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

# 前台天猫-用户

<a id="opIduserUpdateUsingPOST"></a>

## POST 前台天猫-用户详情更新

POST /user/update

前台天猫-用户详情更新

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|user_address|query|string| 是 |user_address|
|user_birthday|query|string| 是 |user_birthday|
|user_gender|query|string| 是 |user_gender|
|user_nickname|query|string| 是 |user_nickname|
|user_password|query|string| 是 |user_password|
|user_profile_picture_src|query|string| 否 |user_profile_picture_src|
|user_realname|query|string| 是 |user_realname|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIduploadUserHeadImageUsingPOST"></a>

## POST 前台天猫-用户更换头像

POST /user/uploadUserHeadImage

前台天猫-用户更换头像

> Body 请求参数

```yaml
file: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |file|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

<a id="opIdgoToUserDetailUsingGET"></a>

## GET 转到前台天猫-用户详情页

GET /userDetails

转到前台天猫-用户详情页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|creationTime|query|integer(int64)| 否 |none|
|id|query|string| 否 |none|
|lastAccessedTime|query|integer(int64)| 否 |none|
|map|query|string| 否 |map|
|maxInactiveInterval|query|integer(int32)| 否 |none|
|new|query|boolean| 否 |none|
|servletContext.classLoader|query|string| 否 |none|
|servletContext.contextPath|query|string| 否 |none|
|servletContext.defaultSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.effectiveMajorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveMinorVersion|query|integer(int32)| 否 |none|
|servletContext.effectiveSessionTrackingModes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].buffer|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].defaultContentType|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].deferredSyntaxAllowedAsLiteral|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].elIgnored|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].errorOnUndeclaredNamespace|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includeCodas|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].includePreludes|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].isXml|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].pageEncoding|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].scriptingInvalid|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].trimDirectiveWhitespaces|query|string| 否 |none|
|servletContext.jspConfigDescriptor.jspPropertyGroups[0].urlPatterns|query|array[string]| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibLocation|query|string| 否 |none|
|servletContext.jspConfigDescriptor.taglibs[0].taglibURI|query|string| 否 |none|
|servletContext.majorVersion|query|integer(int32)| 否 |none|
|servletContext.minorVersion|query|integer(int32)| 否 |none|
|servletContext.requestCharacterEncoding|query|string| 否 |none|
|servletContext.responseCharacterEncoding|query|string| 否 |none|
|servletContext.serverInfo|query|string| 否 |none|
|servletContext.servletContextName|query|string| 否 |none|
|servletContext.sessionCookieConfig.comment|query|string| 否 |none|
|servletContext.sessionCookieConfig.domain|query|string| 否 |none|
|servletContext.sessionCookieConfig.httpOnly|query|boolean| 否 |none|
|servletContext.sessionCookieConfig.maxAge|query|integer(int32)| 否 |none|
|servletContext.sessionCookieConfig.name|query|string| 否 |none|
|servletContext.sessionCookieConfig.path|query|string| 否 |none|
|servletContext.sessionCookieConfig.secure|query|boolean| 否 |none|
|servletContext.sessionTimeout|query|integer(int32)| 否 |none|
|servletContext.virtualServerName|query|string| 否 |none|
|valueNames|query|array[string]| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.defaultSessionTrackingModes|COOKIE|
|servletContext.defaultSessionTrackingModes|URL|
|servletContext.defaultSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|
|servletContext.effectiveSessionTrackingModes|COOKIE|
|servletContext.effectiveSessionTrackingModes|URL|
|servletContext.effectiveSessionTrackingModes|SSL|

> 返回示例

> 200 Response

```
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|string|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

# 数据模型

<h2 id="tocS_ApiVerCodeResp">ApiVerCodeResp</h2>

<a id="schemaapivercoderesp"></a>
<a id="schema_ApiVerCodeResp"></a>
<a id="tocSapivercoderesp"></a>
<a id="tocsapivercoderesp"></a>

```json
{
  "code": "string",
  "img": "string",
  "verToken": "string"
}

```

ApiVerCodeResp

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|string|false|none||验证码|
|img|string|false|none||验证码图片|
|verToken|string|false|none||verToken|


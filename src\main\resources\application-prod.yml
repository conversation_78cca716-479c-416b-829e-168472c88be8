#Spring Boot Config

#端口
server:
  port: 8082
  servlet:
    context-path: /tmall

spring:
  datasource:
    #druid基本属性
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************
    username: root
    password:
    #druid相关配置
    druid:
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall
      #配置初始化大小/最小/最大
      initial-size: 5
      min-idle: 1
      max-active: 50
      #获取连接等待超时时间
      max-wait: 60000
      #间隔多久进行一次检测，检测需要关闭的空闲连接
      time-between-eviction-runs-millis: 60000
      #一个连接在池中最小生存的时间
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      #打开PSCache，并指定每个连接上PSCache的大小。oracle设为true，mysql设为false。分库分表较多推荐设置为false
      pool-prepared-statements: false
      max-pool-prepared-statement-per-connection-size: 20
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
    hikari:
      connection-timeout: 5000
      max-lifetime: 60000
  mail:
    host:
    username:
    password:
  freemarker:
    suffix: .html
    request-context-attribute: request
    charset: UTF-8
  aop:
    proxy-target-class: true
  devtools:
    restart:
      enabled: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 20MB
